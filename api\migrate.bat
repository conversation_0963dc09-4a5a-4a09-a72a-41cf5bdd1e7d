@echo off
setlocal EnableDelayedExpansion
REM 讀取.env檔案
for /f "tokens=1,2 delims==" %%G in (.env) do (
    set %%G=%%H
)

set MIGRATIONS_DIR=database/migrations

if "%1"=="" (
    echo Usage: migrate [command]
    echo Commands:
    echo   up        - Run all pending migrations
    echo   down      - Rollback last migration
    echo   force     - Force set database version
    echo   create    - Create new migration file
    goto :eof
)

if "%1"=="create" (
    if "%2"=="" (
        echo Error: Missing migration name
        echo Usage: migrate create [migration_name]
        goto :eof
    )
    
    if not exist %MIGRATIONS_DIR% mkdir %MIGRATIONS_DIR%
    
    echo migrate create -ext mysql -dir %MIGRATIONS_DIR% -seq %2
    goto :eof
)

REM 建立連線字串

set MYSQL_DSN=mysql://%DB_USER%:%DB_PASSWORD%@tcp^(localhost:3306^)^/%DB_NAME%

if "%1"=="up" (
    echo migrate -path %MIGRATIONS_DIR% -database "%MYSQL_DSN%" up
)

if "%1"=="down" (
    echo migrate -path %MIGRATIONS_DIR% -database "%MYSQL_DSN%" down 1
)

if "%1"=="force" (
    if "%2"=="" (
        echo Error: Missing version number
        echo Usage: migrate force [version]
        goto :eof
    )
    
    echo migrate -path %MIGRATIONS_DIR% -database "%MYSQL_DSN%" force %2
)

if "%1"=="version" (
    echo migrate -path %MIGRATIONS_DIR% -database "%MYSQL_DSN%" version
)