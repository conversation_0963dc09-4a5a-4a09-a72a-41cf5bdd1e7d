# 跨分頁 Token 同步功能

## 問題描述

在多分頁環境中，當其中一個分頁的 token 過期並自動刷新時，其他分頁無法感知到這個變化，導致其他分頁在發送請求時仍然使用舊的 token，從而收到 401 錯誤並被重定向到登入頁面。

## 解決方案

實現了基於 `localStorage` 的 `storage` 事件的跨分頁同步機制，確保所有分頁的 token 狀態保持一致。

### 核心機制

1. **事件廣播**: 當 token 更新或用戶登出時，通過 `localStorage` 觸發 `storage` 事件
2. **事件監聽**: 所有分頁監聽 `storage` 事件，並同步更新本地的 token 狀態
3. **防循環更新**: 使用 `_isUpdatingFromStorage` 標記避免循環更新

### 實現細節

#### 1. Auth Store 修改

- 添加了 `initCrossTabSync()` 方法來初始化跨分頁同步監聽器
- 修改了 `updateToken()` 方法來廣播 token 更新事件
- 修改了 `logout()` 方法來廣播登出事件
- 添加了防循環更新機制

#### 2. 同步事件

- `auth-token-sync`: Token 更新同步事件
- `auth-logout-sync`: 登出同步事件

#### 3. Axios 配置

- 在 `boot/axios.ts` 中初始化跨分頁同步監聽器

## 使用方法

### 自動同步

系統會自動處理跨分頁同步，無需額外配置。當任何分頁的 token 更新或用戶登出時，所有其他分頁會自動同步狀態。

### 測試功能

訪問 `/cross-tab-test` 頁面可以測試跨分頁同步功能：

1. 在多個分頁中打開測試頁面
2. 在其中一個分頁點擊"測試 Token 更新"或"模擬 Token 刷新"
3. 觀察其他分頁是否同步更新了 token 狀態
4. 測試登出同步功能

### 開發者工具

在開發環境下，可以通過瀏覽器控制台使用 `window.crossTabSyncTester` 來進行測試：

```javascript
// 測試 token 同步
window.crossTabSyncTester.testTokenSync();

// 測試登出同步
window.crossTabSyncTester.testLogoutSync();

// 查看監聽器狀態
window.crossTabSyncTester.getListenersStatus();
```

## 技術細節

### Storage 事件機制

- 使用 `localStorage.setItem()` 觸發 `storage` 事件
- 事件只在其他分頁中觸發，不在當前分頁中觸發
- 使用臨時的 localStorage 項目來傳遞數據，並在短時間內清除

### 數據結構

#### Token 同步數據
```typescript
{
  access_token: string;
  expires_at: Date;
  refresh_token: string;
  refresh_expires_at: Date;
  timestamp: number;
}
```

#### 登出同步數據
```typescript
"true" // 簡單的字符串標記
```

### 兼容性

- 保持與現有 localStorage 存儲的向後兼容
- 支持 Pinia 持久化插件
- 不影響現有的 token 管理邏輯

## 注意事項

1. **瀏覽器支持**: 需要瀏覽器支持 `storage` 事件（現代瀏覽器都支持）
2. **同源策略**: 只在相同域名下的分頁間同步
3. **性能影響**: 同步事件的性能影響很小，但在高頻更新時需要注意
4. **調試**: 可以通過瀏覽器開發者工具的 Application > Local Storage 查看同步事件

## 故障排除

### 常見問題

1. **同步不工作**
   - 檢查瀏覽器是否支持 `storage` 事件
   - 確認所有分頁都在相同域名下
   - 檢查控制台是否有錯誤信息

2. **循環更新**
   - 檢查 `_isUpdatingFromStorage` 標記是否正常工作
   - 確認沒有其他代碼干擾 localStorage

3. **性能問題**
   - 檢查是否有過多的同步事件
   - 確認臨時 localStorage 項目是否正常清除

### 調試方法

1. 打開瀏覽器開發者工具
2. 在 Console 中查看同步事件日誌
3. 在 Application > Local Storage 中監控存儲變化
4. 使用測試頁面進行功能驗證

## 未來改進

1. 添加更詳細的錯誤處理
2. 實現更精細的同步控制
3. 添加同步狀態的可視化指示器
4. 支持更多類型的跨分頁同步需求
