<template>
  <router-view />
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import { pwaInstallService } from './services/pwaInstall';
import { updateChecker } from './services/updateChecker';
import { versionChecker } from './services/versionChecker';

// 在應用啟動時初始化
onMounted(() => {
  console.log('初始化 PWA 服務');

  // 初始化 PWA 安裝服務
  pwaInstallService.initialize();

  // 初始化自動更新檢查器
  console.log('啟動自動更新檢查器');

  // 啟動版本檢查器
  versionChecker.startVersionCheck();

  // 監聽來自 Service Worker 的消息
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && (event.data.type === 'RELOAD_PAGE' || event.data.type === 'FORCE_RELOAD')) {
        console.log('收到 Service Worker 重新載入頁面的請求:', event.data.type);
        // 立即重新載入頁面
        window.location.reload();
      }
    });

    // 監聽 Service Worker 狀態變化
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('Service Worker 控制器已變更，重新載入頁面');
      window.location.reload();
    });
  }
});

// 清理資源
onUnmounted(() => {
  updateChecker.destroy();
  versionChecker.destroy();
});

defineOptions({
  name: 'App',
});
</script>
