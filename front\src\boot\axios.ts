import { boot } from 'quasar/wrappers';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/auth';
import AUTH_API from '@/api/modules/auth';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
    $api: AxiosInstance;
  }
}

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

export default boot(({ app, router }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API

  // 初始化跨分頁同步
  const authStore = useAuthStore();
  authStore.initCrossTabSync();

  let isRefreshing = false;
  // 待處理請求佇列
  let failedQueue: Array<{
    resolve: (value: unknown) => void;
    reject: (reason?: unknown) => void;
  }> = [];

  // 處理佇列中的請求
  const processQueue = (error: unknown, token: string | null = null) => {
    failedQueue.forEach((prom) => {
      if (error) {
        prom.reject(error);
      } else {
        prom.resolve(token);
      }
    });

    // 清空佇列
    failedQueue = [];
  };

  // 请求拦截器
  api.interceptors.request.use(
    (config) => {
      const authStore = useAuthStore();
      const accessToken = authStore.accessToken;
      if (accessToken) {
        config.headers['Authorization'] = `Bearer ${accessToken}`;
        config.headers['X-Refresh-Token'] = authStore.refreshToken;
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  api.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      switch (error.response?.status) {
        case 401:
          // Unauthorized
          return handle401(originalRequest);
        case 403:
          // Forbidden
          router.push('/');
          break;
        default:
          break;
      }

      return Promise.reject(error);
    }
  );

  const handle401 = async (
    originalRequest: AxiosRequestConfig & { _retry?: boolean }
  ) => {
    // 不包含的url
    const excludeUrls = ['auth/logout', 'auth/refresh-token'];

    // 如果是 401 錯誤且不是刷新 token 的請求
    if (
      !originalRequest._retry &&
      !excludeUrls.some((url) => originalRequest.url?.includes(url))
    ) {
      originalRequest._retry = true;

      const authStore = useAuthStore();
      // 如果目前沒有正在進行的重新整理請求
      if (!isRefreshing) {
        isRefreshing = true;

        try {
          // 嘗試刷新 token
          const refreshToken = localStorage.getItem('refreshToken');
          if (!refreshToken) {
            throw new Error('No refresh token');
          }

          const response = await AUTH_API.refreshToken();
          const newToken = response.data;

          // 更新 token
          authStore.updateToken(newToken);

          // 設置重新整理狀態為 false
          isRefreshing = false;

          // 處理所有排隊的請求
          processQueue(null, newToken.access_token);

          // 重試原始請求
          if (!originalRequest.headers) {
            originalRequest.headers = {};
          }
          originalRequest.headers[
            'Authorization'
          ] = `Bearer ${newToken.access_token}`;
          originalRequest.headers['X-Refresh-Token'] = newToken.refresh_token;
          return api(originalRequest);
        } catch (refreshError) {
          // 刷新失敗，處理所有排隊的請求（拒絕）
          isRefreshing = false;
          processQueue(refreshError, null);

          // 登出並跳轉到登入頁
          authStore.logout();
          router.push('/login');
          return Promise.reject(refreshError);
        }
      } else {
        // 已經有一個重新整理 token 的請求正在進行中
        // 將這個請求放入佇列中，等待重新整理完成後再處理
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            if (!originalRequest.headers) {
              originalRequest.headers = {};
            }
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            originalRequest.headers['X-Refresh-Token'] = authStore.refreshToken;
            return api(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }
    }
  };
});

export { api };
