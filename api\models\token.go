package models

import (
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

var (
	accessTokenTTL  = 15 * time.Minute
	refreshTokenTTL = 7 * 24 * time.Hour

	ErrTokenInvalid   = errors.New("token is invalid")
	ErrTokenRevoked   = errors.New("token has been revoked")
	ErrTokenExpired   = errors.New("token has expired")
	ErrRefreshInvalid = errors.New("refresh token is invalid")
)

type AccessToken struct {
	ID          uint64    `gorm:"primaryKey" form:"id" json:"id"`
	UserID      uint64    `json:"user_id"`
	DeviceID    string    `json:"device_id"`
	TokenID     string    `json:"token_id"`
	AccessToken string    `json:"access_token"`
	UserAgent   string    `json:"user_agent"`
	IPAddress   string    `json:"ip_address"`
	LastUsedAt  time.Time `json:"last_used_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	IsRevoked   bool      `json:"is_revoked"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type RefreshToken struct {
	ID            uint64    `gorm:"primaryKey" form:"id" json:"id"`
	AccessTokenID uint64    `json:"access_token_id"`
	RefreshToken  string    `json:"refresh_token"`
	ExpiresAt     time.Time `json:"expires_at"`
	IsRevoked     bool      `json:"is_revoked"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type TokenClaims struct {
	UserID  uint64 `json:"user_id"`
	IsAdmin bool   `json:"is_admin"`
	jwt.RegisteredClaims
}

type TokenService struct {
	db *gorm.DB
}

type TokenPair struct {
	AccessToken      string    `json:"access_token"`
	ExpiresAt        time.Time `json:"expires_at"`
	RefreshToken     string    `json:"refresh_token"`
	RefreshExpiresAt time.Time `json:"refresh_expires_at"`
}

func GetTokenFromHeader(c *gin.Context) string {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return ""
	}

	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {

		return ""
	}

	return parts[1]
}

func NewTokenService(db *gorm.DB) *TokenService {
	return &TokenService{db: db}
}

func getSecretKey() []byte {
	return []byte(viper.GetString("AUTH_SECRET"))
}

func getRefreshKey() []byte {
	return []byte(viper.GetString("AUTH_REFRESH_SECRET"))
}

func (s *TokenService) GenerateTokenPair(user User, userAgent, ipAddress, deviceID string) (TokenPair, error) {
	tokenPair := TokenPair{}

	// 使用 transaction 確保數據一致性
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 生成唯一的 JWT ID
		jti := generateUUID()
		issuer := viper.GetString("APP_DOMAIN")

		// 生成訪問令牌
		accessClaims := TokenClaims{
			UserID:  user.ID,
			IsAdmin: user.IsAdmin,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(accessTokenTTL)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
				Issuer:    issuer,
				Subject:   user.UID,
				ID:        jti,
			},
		}

		// 生成刷新令牌
		refreshClaims := jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(refreshTokenTTL)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    issuer,
			Subject:   user.UID,
			ID:        generateUUID(),
		}

		accessToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims).SignedString(getSecretKey())
		if err != nil {
			return err
		}

		refreshToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims).SignedString(getRefreshKey())
		if err != nil {
			return err
		}

		tokenPair = TokenPair{
			AccessToken:      accessToken,
			ExpiresAt:        accessClaims.ExpiresAt.Time,
			RefreshToken:     refreshToken,
			RefreshExpiresAt: refreshClaims.ExpiresAt.Time,
		}

		// 存儲令牌信息
		tokenRecord := AccessToken{
			UserID:      user.ID,
			DeviceID:    deviceID,
			TokenID:     jti,
			AccessToken: accessToken,
			UserAgent:   userAgent,
			IPAddress:   ipAddress,
			LastUsedAt:  time.Now(),
			ExpiresAt:   accessClaims.ExpiresAt.Time,
		}

		if err := tx.Create(&tokenRecord).Error; err != nil {
			return err
		}

		// 存儲刷新令牌信息
		refreshTokenRecord := RefreshToken{
			AccessTokenID: tokenRecord.ID,
			RefreshToken:  refreshToken,
			ExpiresAt:     refreshClaims.ExpiresAt.Time,
		}

		return tx.Create(&refreshTokenRecord).Error
	})

	if err != nil {
		return tokenPair, err
	}

	return tokenPair, nil
}

func generateUUID() string {
	return uuid.New().String()
}

func (s *TokenService) ValidateToken(tokenString string) (*TokenClaims, bool, error) {
	claims := &TokenClaims{}

	_, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrTokenInvalid
		}
		return getSecretKey(), nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, true, ErrTokenExpired
		}
		return nil, false, ErrTokenInvalid
	}

	var tokenRecord AccessToken
	if err := s.db.Where("token_id = ? AND is_revoked = ?", claims.ID, false).First(&tokenRecord).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, false, ErrTokenRevoked
		}
		return nil, false, err
	}

	// 更新最後使用時間
	if err := s.db.Model(&tokenRecord).Update("last_used_at", time.Now()).Error; err != nil {
		return nil, false, err
	}

	return claims, false, nil
}

func (s *TokenService) ParseToken(tokenString string) (*TokenClaims, bool, error) {
	claims, isExpired, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, isExpired, err
	}

	return claims, isExpired, nil
}

func (s *TokenService) RevokeToken(tokenID string) error {
	return s.db.Model(&AccessToken{}).Where("token_id = ?", tokenID).Update("is_revoked", true).Error
}

func (s *TokenService) RevokeAllUserTokens(userID uint) error {
	return s.db.Model(&AccessToken{}).Where("user_id = ?", userID).Update("is_revoked", true).Error
}

func (s *TokenService) RefreshToken(refreshToken string) (TokenPair, error) {
	// 驗證刷新令牌
	claims := &jwt.RegisteredClaims{}
	token, err := jwt.ParseWithClaims(refreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrRefreshInvalid
		}
		return getRefreshKey(), nil
	})

	if err != nil || !token.Valid {
		return TokenPair{}, ErrRefreshInvalid
	}

	// 在資料庫中查找刷新令牌
	var refreshTokenRecord RefreshToken
	if err := s.db.Where("refresh_token = ? AND is_revoked = ?", refreshToken, false).First(&refreshTokenRecord).Error; err != nil {
		return TokenPair{}, ErrRefreshInvalid
	}

	// 檢查令牌是否過期
	if time.Now().After(refreshTokenRecord.ExpiresAt) {
		return TokenPair{}, ErrTokenExpired
	}

	// 查找關聯的訪問令牌
	var accessTokenRecord AccessToken
	if err := s.db.First(&accessTokenRecord, refreshTokenRecord.AccessTokenID).Error; err != nil {
		return TokenPair{}, err
	}

	// 在交易中處理令牌更新
	var newTokenPair TokenPair
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 撤銷舊的刷新令牌
		if err := tx.Model(&refreshTokenRecord).Update("is_revoked", true).Error; err != nil {
			return err
		}

		// 撤銷舊的訪問令牌
		if err := tx.Model(&accessTokenRecord).Update("is_revoked", true).Error; err != nil {
			return err
		}

		// 查找用戶信息
		var user User
		if err := tx.First(&user, accessTokenRecord.UserID).Error; err != nil {
			return err
		}

		// 生成新的令牌對
		var err error
		newTokenPair, err = s.GenerateTokenPair(user, accessTokenRecord.UserAgent, accessTokenRecord.IPAddress, accessTokenRecord.DeviceID)
		return err
	})

	if err != nil {
		return TokenPair{}, err
	}

	return newTokenPair, nil
}
