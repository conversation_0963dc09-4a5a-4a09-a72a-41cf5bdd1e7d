package routes

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	. "lottery/controllers"
	"lottery/middleware"
)

func Routes() *gin.Engine {
	r := gin.Default()

	// CORS 設定
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	api := r.Group("/api")
	renderApiRoutes(api)

	return r
}

func renderApiRoutes(api *gin.RouterGroup) {
	auth := api.Group("/auth")
	{
		auth.POST("/register", RegisterHandler)
		auth.POST("/login", LoginHandler)

		auth.POST("/refresh-token", RefreshTokenHandler)

		auth.POST("/logout", LogoutHandler)
	}

	api.Use(middleware.TokenAuth()).
		Use(middleware.AuthRequired())

	// 用戶個人資料相關端點
	api.GET("/profile", GetCurrentUserProfile)
	api.PATCH("/profile", UpdateCurrentUserProfile)
	api.PATCH("/profile/password", ChangePassword)

	api.GET("/lotto", GetLotto)
	api.GET("/lotto/predict", GetLottoPredict)
	api.GET("/lotto/latest", GetLatestLottoResults)
	api.GET("/ws", middleware.WebSocketAuth(), HandleWebSocket)

	admin := api.Group("/admin")
	admin.Use(middleware.AdminOnly())

	user := admin.Group("/users")
	{
		user.GET("/", GetUserList)
		user.PATCH("/", UpdateUserByAdmin)
		user.PATCH("/:id/active", UpdateUserActive)
		user.PATCH("/:id/expire", UpdateUserExpire)
		user.DELETE("/:id", DeleteUser)
		user.GET("/:id/devices", GetUserDevices)
		user.GET("/:id/login-history", GetUserLoginHistory)
		user.DELETE("/:id/devices/:device_id", DeleteUserDevice)
	}
}
