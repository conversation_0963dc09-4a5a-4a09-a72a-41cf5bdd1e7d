import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: () => import('layouts/LoginLayout.vue'),
    children: [{ path: '', component: () => import('pages/LoginPage.vue') }],
  },
  {
    path: '/register',
    component: () => import('layouts/LoginLayout.vue'),
    children: [{ path: '', component: () => import('pages/RegisterPage.vue') }],
  },
  {
    path: '/',
    redirect: '/lotto-results',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: 'index', component: () => import('pages/IndexPage.vue') },
      { path: 'lotto-results', component: () => import('pages/LottoResultsPage.vue') },
      { path: 'lotto-detail/:drawType', component: () => import('pages/LottoDetailPage.vue') },
      { path: 'profile', component: () => import('pages/ProfilePage.vue') },
      { path: 'rd1', component: () => import('pages/BallFollowPage.vue') },
      { path: 'tail', component: () => import('pages/TailPage.vue') },
      { path: 'pattern', component: () => import('pages/PatternPage.vue') },
      { path: 'install', component: () => import('pages/InstallmentPage.vue') },
      { path: 'update-test', component: () => import('pages/UpdateTestPage.vue') },
      { path: 'cross-tab-test', component: () => import('pages/CrossTabTestPage.vue') },
      {
        path: 'disclaimer',
        component: () => import('pages/DisclaimerPage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/admin/dashboard',
    redirect: '/admin/dashboard/user',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'user',
        component: () => import('pages/admin/UserPage.vue'),
      },
      {
        path: 'batch-analysis',
        component: () => import('pages/admin/BatchAnalysisPage.vue'),
      },
    ],
    meta: { requiresAuth: true, requiresAdmin: true },
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
