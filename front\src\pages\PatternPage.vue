<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <template v-if="store.getLotto?.draw_date">
          <div class="row lto-ref q-mb-sm">
            <div class="col-12 col-sm-4 self-center text-h6">
              <div>{{ store.getDrawLabel }}</div>
              <span>參考期號：</span>
              <span>{{ lottoRefer?.period }}</span>
              <span>（{{ lottoRefer?.draw_date }}）</span>
            </div>
            <div class="col-12 col-sm-6 self-center text-subtitle1">
              <div class="row balls">
                <div
                  class="col-auto"
                  v-for="number in lottoRefer?.draw_number_size"
                  :key="number"
                >
                  <div class="ball">
                    {{ paddingZero(number) }}
                  </div>
                </div>
                <div class="col-auto" v-if="lottoRefer?.special_number">
                  <div
                    class="ball special-number"
                    :key="lottoRefer?.special_number"
                  >
                    {{ paddingZero(lottoRefer?.special_number) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 重新選擇參考 -->
          <div class="row q-mb-md">
            <div class="col">
              <q-btn
                type="button"
                label="重新選擇"
                color="primary"
                class="text-h6 q-ml-md"
                @click="reselectRef"
                v-if="!isReselect"
              />
              <q-btn
                type="button"
                label="取消選擇"
                color="negative"
                class="text-h6 q-ml-md"
                @click="cancelReselect"
                v-else
              />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="row q-mb-md">
            <div class="text-h6">※請選擇參考期號</div>
          </div>
        </template>

        <q-separator class="q-mb-md" />

        <template v-if="!isReselect && store.getLotto?.draw_date">
          <div class="row q-mb-md">
            <div class="col-12 text-h5 text-weight-bolder text-center">
              綜合分析設定
            </div>
          </div>

          <!-- 獎號拖牌 -->
          <div class="row q-mb-md">
            <div class="col-12 text-h6 text-weight-bold">獎號拖牌組合</div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="comb1"
                :options="combOptions"
                emit-value
                map-options
              />
            </div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="comb2"
                :options="combOptions"
                emit-value
                map-options
              />
            </div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="comb3"
                :options="combOptions"
                emit-value
                map-options
              />
            </div>
          </div>
          <!-- 尾數拖牌 -->
          <div class="row q-mb-md">
            <div class="col-12 text-h6 text-weight-bold">尾數拖牌組合</div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="tailComb1"
                :options="tailCombOptions"
                emit-value
                map-options
              />
            </div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="tailComb2"
                :options="tailCombOptions"
                emit-value
                map-options
              />
            </div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="tailComb3"
                :options="tailCombOptions"
                emit-value
                map-options
              />
            </div>
          </div>

          <div class="row q-mb-md">
            <!-- 推算期數 -->
            <div class="col-12 col-sm-4">
              <div class="text-h6 text-weight-bold">推算期數</div>
              <div class="q-pa-sm">
                <q-select
                  outlined
                  dense
                  v-model="period"
                  :options="periodOptions"
                  input-debounce="0"
                  use-input
                  hide-selected
                  fill-input
                  @filter="onPeriodFilter"
                  emit-value
                  map-options
                >
                  <template v-slot:no-option>
                    <q-item>
                      <q-item-section class="text-grey">
                        無可用選項
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
              </div>
            </div>
            <!-- 最大區間 -->
            <div class="col-12 col-sm-4">
              <div class="text-h6 text-weight-bold">最大區間</div>
              <div class="q-pa-sm">
                <q-select
                  outlined
                  dense
                  v-model="maxRange"
                  :options="maxRangeOptions"
                  emit-value
                  map-options
                />
              </div>
            </div>
            <!-- 預測期數 -->
            <div class="col-12 col-sm-4">
              <div class="text-h6 text-weight-bold">預測期數</div>
              <div class="q-pa-sm">
                <q-select
                  outlined
                  dense
                  v-model="ahead"
                  :options="aheadOptions"
                  emit-value
                  map-options
                />
              </div>
            </div>
          </div>

          <!-- 按鈕 -->
          <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
            <q-btn
              type="button"
              label="中斷計算"
              color="negative"
              class="text-h6 q-mr-md"
              @click="stopCalculating"
              v-if="progressStore.isCalculating"
            />

            <q-btn
              type="button"
              label="開始計算"
              color="positive"
              class="text-h6 q-px-lg q-py-sm"
              @click="doCalculate"
              :loading="progressStore.isCalculating"
            >
              <template v-slot:loading>
                <q-spinner-dots />
              </template>
            </q-btn>
          </q-card-actions>
        </template>

        <IndexPage
          :draw-type-query="store.drawType"
          :date-query="lottoRefer?.draw_date || ''"
          :is-select-ref="true"
          @select-ref="selectRef"
          v-else
        />
      </q-card-section>

      <!-- 進度條 -->
      <q-card-section v-if="progressStore.isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressStore.progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progressStore.progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <template v-if="!progressStore.isCalculating && drawResults.length > 0">
      <PatternResult
        :is-super-lotto="isSuperLotto"
        :draw-results="drawResults"
        :predict-result="predictResult"
        :rd-results="rdResults"
        :tail-rd-results="tailRdResults"
      />
    </template>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useLottoStore } from '@/stores/lotto';
import { useProgressStore } from '@/stores/progress';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { StatResult, ProgressInfo, WarningInfo } from '@/models/types';
import { paddingZero } from '@/utils';
import {
  useLotteryAnalysis
} from '@/composables/useLotteryAnalysis';
import IndexPage from './IndexPage.vue';
import PatternResult from '@/components/PatternResult.vue';

const store = useLottoStore();
const lottoRefer = ref<LottoItem | null>(store.getLotto);
const progressStore = useProgressStore();

const analysis = useLotteryAnalysis();

const comb1 = ref(1);
const comb2 = ref(1);
const comb3 = ref(1);

const tailComb1 = ref(1);
const tailComb2 = ref(1);
const tailComb3 = ref(1);

// 獎號組合
const combOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
];
// 尾數組合
const tailCombOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
  { label: '四星組合', value: 4 },
  { label: '五星組合', value: 5 },
];

const isReselect = ref(false);
const reselectRef = () => {
  isReselect.value = true;
};

const selectRef = () => {
  isReselect.value = false;
};

const cancelReselect = () => {
  isReselect.value = false;
};

watch(
  () => store.getLotto,
  (val) => {
    if (val) {
      lottoRefer.value = val;
    }
  }
);

watch(
  () => lottoRefer.value?.period,
  () => {
    drawResults.value = [];
  }
);

// 推算期數(10-2000)
const period = ref(50);
let periodOptions = ref(
  Array.from({ length: 1991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);
const onPeriodFilter = (
  val: string,
  update: (callbackFn: () => void, afterFn?: (ref: unknown) => void) => void,
  abort: () => void
) => {
  const num = parseInt(val, 10);
  if (num < 10 || num > 2000) {
    abort();
  }

  update(() => {
    periodOptions.value = Array.from({ length: 1991 }, (_, i) => i + 10)
      .filter((n) => n.toString().startsWith(val))
      .map((n) => ({ label: `${n.toString()}期`, value: n }));
  });
};

// 最大區間(10-30)
const maxRange = ref(20);
const maxRangeOptions = Array.from({ length: 21 }, (_, i) => ({
  label: `${i + 10}期`,
  value: i + 10,
}));

// 預測期數(1-15)
const ahead = ref(1);
const aheadOptions = Array.from({ length: 15 }, (_, i) => ({
  label: `下${i + 1}期`,
  value: i + 1,
}));

const isSuperLotto = ref(false);
const drawResults = ref<LottoItem[]>([]);
const predictResult = ref<LottoItem>({
  period: '',
  draw_date: '',
  draw_number_appear: [],
  draw_number_size: [],
  tails: new Map(),
});

const rdResults = ref<StatResult[]>([]);
const tailRdResults = ref<StatResult[]>([]);
const doCalculate = async () => {
  try {
    progressStore.startCalculating();
    isSuperLotto.value = store.isSuperLotto;

    const response = await LOTTO_API.getLottoList({
      draw_type: store.getDrawType,
      date_end: lottoRefer.value?.draw_date ?? '',
      limit: period.value,
    });

    drawResults.value = response.data.reverse();

    const predictResponse = await LOTTO_API.getLottoPredict({
      draw_type: store.getDrawType,
      draw_date: store.getLotto?.draw_date,
      ahead_count: ahead.value,
    });

    predictResult.value = predictResponse.data;

    if (predictResult.value.period) {
      predictResult.value.tailSet = analysis.getTailSet(
        predictResult.value,
        isSuperLotto.value
      );
    }

    const rdResponse = await doRdCalculating();
    rdResults.value = rdResponse.data;

    const tailRdResponse = await doTailRdCalculating();
    tailRdResults.value = tailRdResponse.data;
  } catch (error) {
    console.error('計算錯誤:', error);
  } finally {
    stopCalculating();
  }
};

// 獎號拖牌
const doRdCalculating = () => {
  const serializedDrawResults = drawResults.value.map((item: LottoItem) => {
    const nums = [...item.draw_number_size];
    if (item.special_number && !store.isSuperLotto) {
      // 除威力彩，其餘有彩種皆須加入特別號
      nums.push(item.special_number);
    }
    // 返回一個純粹的對象
    return {
      numbers: [...nums], // 確保創建新數組
      period: String(item.period), // 確保 period 是字符串
    };
  });

  // 更新本地狀態
  analysis.setResults(serializedDrawResults);
  // 設置配置
  analysis.setConfig({
    firstGroupSize: comb1.value,
    secondGroupSize: comb2.value,
    targetGroupSize: comb3.value,
    maxRange: maxRange.value,
    lookAheadCount: ahead.value,
  });

  // 使用防抖動的進度更新
  let lastUpdate = Date.now();
  const debounceInterval = 8; // 約 120 fps

  return analysis.analyzeWithProgress(
    async (info: ProgressInfo) => {
      const now = Date.now();
      if (now - lastUpdate >= debounceInterval) {
        await progressStore.updateProgress(info);
        lastUpdate = now;
      }
    },
    (warning: WarningInfo) => {
      progressStore.addWarning(warning);
    }
  );
};

// 尾數拖牌
const doTailRdCalculating = () => {
  // 創建數據結構
  const serializedTailResults = drawResults.value.map((item: LottoItem) => {
    const numbers = new Set<number>();

    for (let number of item.draw_number_size) {
      numbers.add(number % 10);
    }

    if (!store.isSuperLotto && item.special_number) {
      numbers.add(item.special_number % 10);
    }

    const sorted = Array.from(numbers).sort((a, b) => {
      if (a === 0) return 1;
      if (b === 0) return -1;
      return a - b;
    });

    return {
      period: String(item.period),
      numbers: [...sorted],
    };
  });

  analysis.init(
    {
      firstGroupSize: tailComb1.value,
      secondGroupSize: tailComb2.value,
      targetGroupSize: tailComb3.value,
      maxRange: maxRange.value,
      lookAheadCount: ahead.value,
    },
    serializedTailResults
  );

  // 使用防抖動的進度更新
  let lastUpdate = Date.now();
  const debounceInterval = 8; // 約 120 fps

  return analysis.analyzeWithProgress(
    async (info: ProgressInfo) => {
      const now = Date.now();
      if (now - lastUpdate >= debounceInterval) {
        await progressStore.updateProgress(info);
        lastUpdate = now;
      }
    },
    (warning: WarningInfo) => {
      progressStore.addWarning(warning);
    }
  );
};

const stopCalculating = () => {
  progressStore.stopCalculating();
  analysis.stopAnalyzer();
};
</script>
