package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	. "lottery/utils"
)

var dsn string

func init() {
	viper.SetConfigName(".env")
	viper.SetConfigType("env")
	viper.AddConfigPath(".")
	viper.AddConfigPath("..")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	dsn = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci&multiStatements=true",
		viper.Get("DB_USER"),
		viper.Get("DB_PASSWORD"),
		viper.Get("DB_HOST"),
		viper.Get("DB_PORT"),
		viper.Get("DB_NAME"),
	)

	// Migration()
}

func ConnectDB() *gorm.DB {
	dbLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold: time.Second,  // Slow SQL threshold
			LogLevel:      logger.Error, // Log level
			Colorful:      true,         // Disable color
		},
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: dbLogger,
	})
	if err != nil {
		ErrorLog(ErrorMsg{
			Error: err.Error(),
			Msg:   "Can't connect to database",
		})
		return db
	}

	snowflakePlugin, err := NewSnowflakePlugin()
	if err != nil {
		ErrorLog(ErrorMsg{
			Error: err.Error(),
			Msg:   "Can't create SnowflakePlugin",
		})
	}

	// 注册插件
	err = db.Use(snowflakePlugin)
	if err != nil {
		ErrorLog(ErrorMsg{
			Error: err.Error(),
			Msg:   "Can't use SnowflakePlugin",
		})
	}

	return db
}

func CloseDB(db *gorm.DB) {
	sqlDB, err := db.DB()
	if err != nil {
		ErrorLog(ErrorMsg{
			Error: err.Error(),
			Msg:   "無法關閉資料庫連線",
		})
	}

	if err := sqlDB.Close(); err != nil {
		ErrorLog(ErrorMsg{
			Error: err.Error(),
			Msg:   "無法關閉資料庫連線",
		})
	}
}
