// app global css in SCSS form
html {
  background-color: #f5f5f5;
}

* {
  touch-action: manipulation;
  -webkit-touch-callout: none;
}

.q-field {
  &.q-field--readonly {
    .q-field__control {
      &:before {
        border-style: solid;
      }
    }
  }
}

.q-select {
  font-size: 1.3rem;
}

.scroll {
  .q-item {
    font-size: 1.1rem;
  }
}

.q-tab__label {
  font-size: 1.1rem;
}

.q-header {
  background-color: #3460f2;
}

.ball {
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  padding: 0;
  margin-right: 0.5rem;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd54f 0%, #ffc107 70%, #ff8f00 100%);
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 2px rgba(255, 255, 255, 0.4),
    inset 0 -1px 2px rgba(0, 0, 0, 0.05);
  font-size: 1.8rem;
  font-weight: bold;
  color: #2e2e2e;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
  position: relative;

  // &:before {
  //   content: '';
  //   position: absolute;
  //   top: 20%;
  //   left: 25%;
  //   width: 25%;
  //   height: 25%;
  //   background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  //   border-radius: 50%;
  // }
}

.special-number {
  background: linear-gradient(135deg, #ff6f00 0%, #e65100 50%, #bf360c 100%);
  color: #f5f5f5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.3),
    inset 0 -1px 3px rgba(0, 0, 0, 0.15);

  &:before {
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.25) 50%, transparent 70%);
  }
}

.tail-number {
  background: linear-gradient(135deg, #81c784 0%, #4caf50 50%, #2e7d32 100%);
  color: #f5f5f5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 3px rgba(255, 255, 255, 0.4),
    inset 0 -1px 3px rgba(0, 0, 0, 0.1);

  &:before {
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  }
}

.appearance {
  .ball {
    width: 2.8rem;
    height: 2.8rem;
    font-size: 1.5rem;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      inset 0 1px 2px rgba(255, 255, 255, 0.4),
      inset 0 -1px 2px rgba(0, 0, 0, 0.05);

    &:before {
      top: 22%;
      left: 28%;
      width: 22%;
      height: 22%;
    }
  }
}

.predict {
  background-color: #c62828;
  color: #f5f5f5;
  font-weight: bolder;

  .ball {
    color: #121212;
  }

  .tail-number {
    color: #f5f5f5;
  }
}

// 統一的 chip 樣式
.first-chip {
  background: linear-gradient(135deg, #42a5f5 0%, #1976d2 50%, #0d47a1 100%) !important;
  color: #f5f5f5 !important;
  font-weight: bold;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.second-chip {
  background: linear-gradient(135deg, #5c6bc0 0%, #3f51b5 50%, #283593 100%) !important;
  color: #f5f5f5 !important;
  font-weight: bold;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.target-chip {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 50%, #4a148c 100%) !important;
  color: #f5f5f5 !important;
  font-weight: bold;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

// 描述區域樣式
.description-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .description-ball {
    width: 2.2rem;
    height: 2.2rem;
    font-size: 1.2rem;
    margin: 0 2px;

    &::before {
      top: 20%;
      left: 25%;
      width: 25%;
      height: 25%;
    }
  }
}

.predict-special-number .ball {
  color: #e53935;
}

.q-dialog {
  .q-card {
    max-width: 100%;
    width: 800px;
  }
}

.custom-selected-item {
  font-size: 0.85rem;
}

.sticky-scroll-table {
  /* bg color is important for th; just specify one */
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th {
    background-color: primary;
  }

  thead tr th {
    position: sticky;
    z-index: 1;
  }

  thead tr:first-child th {
    top: 0;
  }

  /* this is when the loading indicator appears */
  &.q-table--loading thead tr:last-child th {
    /* height of all previous header rows */
    top: 48px;
  }

  /* prevent scrolling behind sticky top row on focus */
  tbody {
    /* height of all previous header rows */
    scroll-margin-top: 48px;
  }
}

@media (max-width: 1199px) {
  .ball {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      inset 0 1px 2px rgba(255, 255, 255, 0.4),
      inset 0 -1px 2px rgba(0, 0, 0, 0.05);
  }
}

@media (max-width: 991px) {
  .ball {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      inset 0 1px 2px rgba(255, 255, 255, 0.4),
      inset 0 -1px 2px rgba(0, 0, 0, 0.05);
  }
}

@media (max-width: 768px) {
  .ball {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.3rem;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 1px rgba(255, 255, 255, 0.3),
      inset 0 -1px 1px rgba(0, 0, 0, 0.03);

    &:before {
      top: 25%;
      left: 30%;
      width: 20%;
      height: 20%;
    }
  }
}
