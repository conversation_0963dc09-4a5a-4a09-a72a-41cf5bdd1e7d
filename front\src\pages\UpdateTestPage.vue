<template>
  <q-page class="flex flex-center">
    <div class="q-pa-md" style="max-width: 600px">
      <q-card>
        <q-card-section>
          <div class="text-h6">自動更新測試頁面</div>
          <div class="text-subtitle2">測試 PWA 自動更新功能</div>
        </q-card-section>

        <q-card-section>
          <div class="q-mb-md">
            <strong>當前版本:</strong> {{ versionInfo.baseVersion }}
          </div>

          <div class="q-mb-md">
            <strong>構建版本:</strong> {{ versionInfo.buildVersion }}
          </div>

          <div class="q-mb-md">
            <strong>構建時間戳:</strong> {{ versionInfo.timestamp }}
          </div>

          <div class="q-mb-md">
            <strong>Service Worker 狀態:</strong> {{ swStatus }}
          </div>

          <div class="q-mb-md">
            <strong>更新檢查狀態:</strong> {{ updateStatus }}
          </div>

          <div class="q-mb-md">
            <strong>最後檢查時間:</strong> {{ lastCheckTime }}
          </div>
        </q-card-section>

        <q-card-actions>
          <q-btn
            color="primary"
            @click="manualUpdateCheck"
            :loading="checking"
          >
            手動檢查更新
          </q-btn>

          <q-btn
            color="secondary"
            @click="clearCaches"
            :loading="clearing"
          >
            清除緩存
          </q-btn>

          <q-btn
            color="negative"
            @click="forceReload"
          >
            強制重載
          </q-btn>

          <q-btn
            color="warning"
            @click="testUpdateDialog"
          >
            測試更新對話框
          </q-btn>
        </q-card-actions>
      </q-card>

      <q-card class="q-mt-md">
        <q-card-section>
          <div class="text-h6">說明</div>
          <ul>
            <li>應用會每30秒自動檢查 Service Worker 更新</li>
            <li>應用會每60秒檢查版本變更</li>
            <li>當檢測到新版本時，會彈出確認對話框讓用戶選擇是否更新</li>
            <li>用戶可以選擇「立即更新」或「稍後更新」</li>
            <li>選擇「稍後更新」會在30分鐘後再次提醒</li>
            <li>使用 <code>useFilenameHashes: true</code> 確保文件版本控制</li>
            <li>點擊「測試更新對話框」可以預覽更新提示的樣式</li>
          </ul>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { updateChecker } from '../services/updateChecker';
import { versionChecker } from '../services/versionChecker';
import { useQuasar } from 'quasar';

const $q = useQuasar();

const currentVersion = ref('');
const versionInfo = ref({
  baseVersion: '',
  buildVersion: '',
  timestamp: ''
});
const swStatus = ref('檢查中...');
const updateStatus = ref('正常');
const lastCheckTime = ref('');
const checking = ref(false);
const clearing = ref(false);

let statusInterval: number | null = null;

onMounted(() => {
  currentVersion.value = versionChecker.getCurrentVersion();
  versionInfo.value = versionChecker.getVersionInfo();
  checkServiceWorkerStatus();

  // 每5秒更新狀態
  statusInterval = window.setInterval(() => {
    checkServiceWorkerStatus();
    lastCheckTime.value = new Date().toLocaleTimeString();
  }, 5000);
});

onUnmounted(() => {
  if (statusInterval) {
    clearInterval(statusInterval);
  }
});

const checkServiceWorkerStatus = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        if (registration.active) {
          swStatus.value = '已啟動';
        } else if (registration.installing) {
          swStatus.value = '安裝中';
        } else if (registration.waiting) {
          swStatus.value = '等待中';
        } else {
          swStatus.value = '未知狀態';
        }
      } else {
        swStatus.value = '未註冊';
      }
    } catch (error) {
      swStatus.value = '檢查失敗';
    }
  } else {
    swStatus.value = '不支援';
  }
};

const manualUpdateCheck = async () => {
  checking.value = true;
  updateStatus.value = '檢查中...';

  try {
    console.log('開始手動檢查更新...');

    let hasUpdate = false;

    // 檢查 Service Worker 更新
    try {
      const hasSwUpdate = await updateChecker.manualCheck();
      console.log('Service Worker 更新檢查結果:', hasSwUpdate);
      if (hasSwUpdate) {
        hasUpdate = true;
        updateStatus.value = '發現 Service Worker 更新';
      }
    } catch (swError) {
      console.warn('Service Worker 更新檢查失敗:', swError);
    }

    // 檢查版本更新
    try {
      const hasVersionUpdate = await versionChecker.manualVersionCheck();
      console.log('版本更新檢查結果:', hasVersionUpdate);
      if (hasVersionUpdate) {
        hasUpdate = true;
        updateStatus.value = '發現版本更新';
      }
    } catch (versionError) {
      console.warn('版本更新檢查失敗:', versionError);
    }

    if (!hasUpdate) {
      updateStatus.value = '已是最新版本';
      $q.notify({
        message: '已是最新版本',
        color: 'info',
        icon: 'check_circle'
      });
    } else {
      updateStatus.value = '發現更新，等待用戶確認';
      $q.notify({
        message: '發現新版本，請在彈出的對話框中確認是否更新',
        color: 'positive',
        icon: 'cloud_download',
        timeout: 5000
      });
    }
  } catch (error) {
    console.error('手動更新檢查失敗:', error);
    updateStatus.value = '檢查失敗: ' + (error instanceof Error ? error.message : String(error));
    $q.notify({
      message: '檢查更新失敗: ' + (error instanceof Error ? error.message : String(error)),
      color: 'negative',
      icon: 'error',
      timeout: 5000
    });
  } finally {
    checking.value = false;
  }
};

const clearCaches = async () => {
  clearing.value = true;

  try {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );

      $q.notify({
        message: '緩存已清除',
        color: 'positive',
        icon: 'delete_sweep'
      });
    }
  } catch (error) {
    $q.notify({
      message: '清除緩存失敗',
      color: 'negative',
      icon: 'error'
    });
  } finally {
    clearing.value = false;
  }
};

const forceReload = () => {
  window.location.reload();
};

const testUpdateDialog = () => {
  // 測試版本更新對話框
  const testDialog = () => {
    const dialog = document.createElement('div');
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `;

    dialogContent.innerHTML = `
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          🎉 發現新版本！
        </div>
        <div style="color: #666; font-size: 14px;">
          應用程式有新版本可用，是否要立即更新？<br>
          <small style="color: #999;">(這是測試對話框)</small>
        </div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="test-update-later" style="
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">稍後更新</button>
        <button id="test-update-now" style="
          padding: 8px 16px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">立即更新</button>
      </div>
    `;

    dialog.appendChild(dialogContent);
    document.body.appendChild(dialog);

    // 綁定按鈕事件
    const updateNowBtn = dialog.querySelector('#test-update-now');
    const updateLaterBtn = dialog.querySelector('#test-update-later');

    updateNowBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      $q.notify({
        message: '測試：用戶選擇立即更新',
        color: 'positive',
        icon: 'check_circle'
      });
    });

    updateLaterBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      $q.notify({
        message: '測試：用戶選擇稍後更新',
        color: 'info',
        icon: 'schedule'
      });
    });
  };

  testDialog();
};
</script>
